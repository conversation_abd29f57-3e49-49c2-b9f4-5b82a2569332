#!/usr/bin/env python3
"""
自动优化检测引擎

该模块实现自动检测优化建议执行效果的核心引擎，包括：
1. 指标收集和基线对比
2. 成功标准验证
3. 检测结果判断和状态更新
4. 检测历史记录管理

作者: Augment Code AI Assistant
创建日期: 2025-08-13
版本: 1.0.0
"""

import sqlite3
import json
import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path

from .success_criteria_parser import SuccessCriteriaParser, ParsedCriteria, CriteriaType
from ..core.optimization_types import OptimizationAction

# 🆕 Phase 2: 导入多维度智能验证组件
try:
    from .metrics_collector import MetricsCollector
    from .weight_calculator import WeightCalculator
    from .trend_analyzer import TrendAnalyzer
    from .comprehensive_evaluator import ComprehensiveEvaluator
except ImportError as e:
    # 如果导入失败，使用模拟类
    MetricsCollector = None
    WeightCalculator = None
    TrendAnalyzer = None
    ComprehensiveEvaluator = None
    logging.warning(f"Phase 2组件导入失败: {e}")

class DetectionStatus(Enum):
    """检测状态枚举"""
    PENDING = "pending"  # 等待检测
    RUNNING = "running"  # 检测中
    COMPLETED = "completed"  # 检测完成
    FAILED = "failed"  # 检测失败
    CANCELLED = "cancelled"  # 检测取消

@dataclass
class DetectionResult:
    """检测结果数据类"""
    action_id: str
    detection_status: DetectionStatus
    success: bool  # 是否成功
    confidence: float  # 置信度 (0.0-1.0)
    metrics_collected: Dict[str, Any]  # 收集的指标
    criteria_results: List[Dict[str, Any]]  # 各项标准的检测结果
    baseline_comparison: Dict[str, Any]  # 与基线的对比
    detection_time: datetime
    execution_duration: float  # 检测耗时(秒)
    error_message: Optional[str] = None  # 错误信息
    recommendations: List[str] = None  # 改进建议

    def __post_init__(self):
        if self.recommendations is None:
            self.recommendations = []

class OptimizationDetectionEngine:
    """自动优化检测引擎"""
    
    def __init__(self, db_path: str = "data/fucai3d.db"):
        """
        初始化检测引擎
        
        Args:
            db_path: 数据库路径
        """
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
        self.criteria_parser = SuccessCriteriaParser()

        # 🆕 Phase 2: 初始化多维度智能验证组件
        self.metrics_collector = None
        self.weight_calculator = None
        self.trend_analyzer = None
        self.comprehensive_evaluator = None

        # 检测配置
        self.detection_config = {
            'default_timeout': 300,  # 默认超时时间(秒)
            'min_confidence_threshold': 0.7,  # 最低置信度阈值
            'baseline_window_hours': 24,  # 基线数据时间窗口(小时)
            'metrics_collection_interval': 30,  # 指标收集间隔(秒)
            # 🆕 Phase 2: 多维度验证配置
            'enable_multi_dimensional': True,  # 启用多维度验证
            'trend_analysis_window': 24,  # 趋势分析时间窗口(小时)
            'weight_adaptation_enabled': True,  # 启用权重自适应
        }

        # 初始化数据库表
        self._init_detection_tables()

        # 🆕 Phase 2: 初始化多维度组件
        self._init_phase2_components()
    
    def _init_detection_tables(self):
        """初始化检测相关数据库表"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建检测日志表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS optimization_detection_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    action_id TEXT NOT NULL,
                    detection_type TEXT NOT NULL,
                    criteria_parsed TEXT NOT NULL,
                    metrics_collected TEXT NOT NULL,
                    comparison_result TEXT NOT NULL,
                    detection_status TEXT NOT NULL,
                    confidence_score REAL,
                    execution_time REAL,
                    error_message TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (action_id) REFERENCES optimization_actions(action_id)
                )
            """)
            
            # 创建基线数据表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS optimization_baseline_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    action_id TEXT NOT NULL,
                    metric_name TEXT NOT NULL,
                    metric_value REAL NOT NULL,
                    metric_unit TEXT,
                    collection_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (action_id) REFERENCES optimization_actions(action_id)
                )
            """)
            
            conn.commit()
            conn.close()
            
            self.logger.info("检测数据库表初始化完成")

        except Exception as e:
            self.logger.error(f"检测数据库表初始化失败: {e}")

    def _init_phase2_components(self):
        """🆕 Phase 2: 初始化多维度智能验证组件"""
        try:
            if not self.detection_config.get('enable_multi_dimensional', True):
                self.logger.info("多维度验证已禁用")
                return

            # 初始化指标收集器
            if MetricsCollector:
                self.metrics_collector = MetricsCollector(self.db_path)
                self.logger.info("指标收集器初始化成功")

            # 初始化权重计算器
            if WeightCalculator:
                self.weight_calculator = WeightCalculator(self.db_path, self.metrics_collector)
                self.logger.info("权重计算器初始化成功")

            # 初始化趋势分析器
            if TrendAnalyzer:
                self.trend_analyzer = TrendAnalyzer(self.db_path, self.metrics_collector)
                self.logger.info("趋势分析器初始化成功")

            # 初始化综合评估器
            if ComprehensiveEvaluator:
                self.comprehensive_evaluator = ComprehensiveEvaluator(self.db_path)
                self.logger.info("综合评估器初始化成功")

            self.logger.info("🎉 Phase 2多维度智能验证组件初始化完成")

        except Exception as e:
            self.logger.error(f"Phase 2组件初始化失败: {e}")
            # 降级到Phase 1模式
            self.detection_config['enable_multi_dimensional'] = False

    async def start_detection(self, action: OptimizationAction) -> DetectionResult:
        """
        启动优化检测
        
        Args:
            action: 优化行动
            
        Returns:
            DetectionResult: 检测结果
        """
        start_time = datetime.now()
        
        try:
            self.logger.info(f"开始检测优化行动: {action.action_id}")
            
            # 1. 解析成功标准
            if not action.success_criteria:
                return self._create_failed_result(
                    action.action_id, "未找到成功标准", start_time
                )
            
            parsed_criteria = self.criteria_parser.parse(action.success_criteria)
            if not parsed_criteria:
                return self._create_failed_result(
                    action.action_id, "成功标准解析失败", start_time
                )
            
            # 2. 收集当前指标
            current_metrics = await self._collect_current_metrics(action)
            
            # 3. 获取基线数据
            baseline_data = await self._get_baseline_data(action)
            
            # 🆕 Phase 2: 多维度智能验证
            if self.detection_config.get('enable_multi_dimensional', True) and self.comprehensive_evaluator:
                # 4a. 执行多维度综合评估
                comprehensive_result = self.comprehensive_evaluator.evaluate_optimization(
                    action, action.success_criteria
                )

                # 4b. 基于综合评估创建检测结果
                detection_result = self._create_result_from_comprehensive_evaluation(
                    action, comprehensive_result, start_time
                )

                self.logger.info(f"🎯 多维度评估完成: {comprehensive_result.result.value}, 分数: {comprehensive_result.overall_score:.3f}")

            else:
                # 4. 执行传统检测判断 (Phase 1)
                detection_result = await self._execute_detection(
                    action, parsed_criteria, current_metrics, baseline_data
                )

            # 5. 更新检测状态
            await self._update_detection_status(action, detection_result)

            # 6. 记录检测日志
            await self._log_detection_result(detection_result)
            
            execution_time = (datetime.now() - start_time).total_seconds()
            detection_result.execution_duration = execution_time
            
            self.logger.info(
                f"检测完成: {action.action_id}, 成功: {detection_result.success}, "
                f"置信度: {detection_result.confidence:.2f}"
            )
            
            return detection_result
            
        except Exception as e:
            self.logger.error(f"检测过程异常: {e}")
            return self._create_failed_result(
                action.action_id, f"检测异常: {str(e)}", start_time
            )

    def _create_result_from_comprehensive_evaluation(self, action: OptimizationAction,
                                                   comprehensive_result: Any,
                                                   start_time: datetime) -> DetectionResult:
        """🆕 Phase 2: 从综合评估结果创建检测结果"""
        try:
            # 映射综合评估结果到检测状态
            if comprehensive_result.result.value == "success":
                status = DetectionStatus.COMPLETED
                success = True
            elif comprehensive_result.result.value == "partial_success":
                status = DetectionStatus.COMPLETED
                success = True  # 部分成功也算成功
            elif comprehensive_result.result.value == "failure":
                status = DetectionStatus.COMPLETED
                success = False
            else:  # inconclusive, pending
                status = DetectionStatus.FAILED
                success = False

            # 构建详细的检测结果
            metrics = {
                "overall_score": comprehensive_result.overall_score,
                "confidence_score": comprehensive_result.confidence_score,
                "confidence_level": comprehensive_result.confidence_level.value,
                "trend_impact": comprehensive_result.trend_impact,
                "prediction_accuracy": comprehensive_result.prediction_accuracy,
                "category_scores": {
                    score.category: {
                        "score": score.score,
                        "weight": score.weight,
                        "weighted_score": score.weighted_score,
                        "confidence": score.confidence
                    }
                    for score in comprehensive_result.category_scores
                }
            }

            # 生成检测消息
            if success:
                message = f"✅ 多维度验证成功 (评分: {comprehensive_result.overall_score:.3f}, 置信度: {comprehensive_result.confidence_score:.3f})"
            else:
                message = f"❌ 多维度验证失败 (评分: {comprehensive_result.overall_score:.3f}, 置信度: {comprehensive_result.confidence_score:.3f})"

            # 添加建议到消息
            if comprehensive_result.recommendations:
                message += f"\n建议: {'; '.join(comprehensive_result.recommendations[:3])}"  # 只显示前3个建议

            execution_time = (datetime.now() - start_time).total_seconds()

            return DetectionResult(
                action_id=action.action_id,
                detection_status=status,
                success=success,
                confidence=comprehensive_result.confidence_score,
                metrics_collected=metrics,
                criteria_results=[],
                baseline_comparison={},
                detection_time=datetime.now(),
                execution_duration=execution_time,
                recommendations=comprehensive_result.recommendations
            )

        except Exception as e:
            self.logger.error(f"从综合评估创建检测结果失败: {e}")
            return self._create_failed_result(action.action_id, f"综合评估处理失败: {e}", start_time)

    async def _collect_current_metrics(self, action: OptimizationAction) -> Dict[str, Any]:
        """
        收集当前系统指标
        
        Args:
            action: 优化行动
            
        Returns:
            Dict[str, Any]: 当前指标数据
        """
        try:
            metrics = {}
            
            # 根据优化类型收集相应指标
            if action.action_type in ["predictive_model_optimization", "algorithm_improve"]:
                # 预测准确率指标
                metrics.update(await self._collect_accuracy_metrics())
            
            if action.action_type in ["performance_optimization", "ui_performance"]:
                # 性能指标
                metrics.update(await self._collect_performance_metrics())
            
            if action.action_type in ["system_stability", "error_handling"]:
                # 稳定性指标
                metrics.update(await self._collect_stability_metrics())
            
            # 通用系统指标
            metrics.update(await self._collect_system_metrics())
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"收集指标失败: {e}")
            return {}
    
    async def _collect_accuracy_metrics(self) -> Dict[str, Any]:
        """收集准确率相关指标"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 查询最近的预测准确率
            cursor.execute("""
                SELECT AVG(accuracy) as avg_accuracy,
                       COUNT(*) as prediction_count
                FROM prediction_performance 
                WHERE created_at >= datetime('now', '-24 hours')
            """)
            
            result = cursor.fetchone()
            conn.close()
            
            if result and result[0] is not None:
                return {
                    "accuracy": float(result[0]),
                    "prediction_count": int(result[1]),
                    "metric_type": "accuracy"
                }
            else:
                return {"accuracy": 0.0, "prediction_count": 0, "metric_type": "accuracy"}
                
        except Exception as e:
            self.logger.error(f"收集准确率指标失败: {e}")
            return {"accuracy": 0.0, "prediction_count": 0, "metric_type": "accuracy"}
    
    async def _collect_performance_metrics(self) -> Dict[str, Any]:
        """收集性能相关指标"""
        try:
            # 这里可以集成现有的性能监控系统
            # 暂时返回模拟数据
            return {
                "response_time": 1.2,  # 秒
                "cpu_usage": 45.0,  # 百分比
                "memory_usage": 60.0,  # 百分比
                "metric_type": "performance"
            }
        except Exception as e:
            self.logger.error(f"收集性能指标失败: {e}")
            return {"response_time": 0.0, "cpu_usage": 0.0, "memory_usage": 0.0}
    
    async def _collect_stability_metrics(self) -> Dict[str, Any]:
        """收集稳定性相关指标"""
        try:
            return {
                "uptime": 99.5,  # 百分比
                "error_rate": 0.1,  # 百分比
                "availability": 99.8,  # 百分比
                "metric_type": "stability"
            }
        except Exception as e:
            self.logger.error(f"收集稳定性指标失败: {e}")
            return {"uptime": 0.0, "error_rate": 100.0, "availability": 0.0}
    
    async def _collect_system_metrics(self) -> Dict[str, Any]:
        """收集通用系统指标"""
        try:
            return {
                "timestamp": datetime.now().isoformat(),
                "system_health": "healthy",
                "active_connections": 10,
                "metric_type": "system"
            }
        except Exception as e:
            self.logger.error(f"收集系统指标失败: {e}")
            return {"timestamp": datetime.now().isoformat(), "system_health": "unknown"}
    
    async def _get_baseline_data(self, action: OptimizationAction) -> Dict[str, Any]:
        """
        获取基线数据
        
        Args:
            action: 优化行动
            
        Returns:
            Dict[str, Any]: 基线数据
        """
        try:
            if action.baseline_data:
                return action.baseline_data
            
            # 如果没有基线数据，从数据库查询历史数据作为基线
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT metric_name, AVG(metric_value) as avg_value
                FROM optimization_baseline_data
                WHERE action_id = ? OR collection_time >= datetime('now', '-24 hours')
                GROUP BY metric_name
            """, (action.action_id,))
            
            results = cursor.fetchall()
            conn.close()
            
            baseline = {}
            for metric_name, avg_value in results:
                baseline[metric_name] = avg_value
            
            return baseline
            
        except Exception as e:
            self.logger.error(f"获取基线数据失败: {e}")
            return {}
    
    async def _execute_detection(
        self,
        action: OptimizationAction,
        parsed_criteria: List[ParsedCriteria],
        current_metrics: Dict[str, Any],
        baseline_data: Dict[str, Any]
    ) -> DetectionResult:
        """
        执行检测判断

        Args:
            action: 优化行动
            parsed_criteria: 解析后的成功标准
            current_metrics: 当前指标
            baseline_data: 基线数据

        Returns:
            DetectionResult: 检测结果
        """
        try:
            criteria_results = []
            overall_success = True
            total_confidence = 0.0

            # 逐个检测成功标准
            for criteria in parsed_criteria:
                result = await self._check_single_criteria(
                    criteria, current_metrics, baseline_data
                )
                criteria_results.append(result)

                if not result['success']:
                    overall_success = False

                total_confidence += result['confidence']

            # 计算平均置信度
            avg_confidence = total_confidence / len(parsed_criteria) if parsed_criteria else 0.0

            # 生成基线对比
            baseline_comparison = self._generate_baseline_comparison(
                current_metrics, baseline_data
            )

            # 生成改进建议
            recommendations = self._generate_recommendations(
                criteria_results, baseline_comparison
            )

            return DetectionResult(
                action_id=action.action_id,
                detection_status=DetectionStatus.COMPLETED,
                success=overall_success,
                confidence=avg_confidence,
                metrics_collected=current_metrics,
                criteria_results=criteria_results,
                baseline_comparison=baseline_comparison,
                detection_time=datetime.now(),
                execution_duration=0.0,  # 将在外层设置
                recommendations=recommendations
            )

        except Exception as e:
            self.logger.error(f"执行检测判断失败: {e}")
            return DetectionResult(
                action_id=action.action_id,
                detection_status=DetectionStatus.FAILED,
                success=False,
                confidence=0.0,
                metrics_collected=current_metrics,
                criteria_results=[],
                baseline_comparison={},
                detection_time=datetime.now(),
                execution_duration=0.0,
                error_message=f"检测判断失败: {str(e)}"
            )

    async def _check_single_criteria(
        self,
        criteria: ParsedCriteria,
        current_metrics: Dict[str, Any],
        baseline_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """检测单个成功标准"""
        try:
            # 获取对应的指标值
            metric_value = current_metrics.get(criteria.metric_name)
            if metric_value is None:
                return {
                    'criteria': criteria.description,
                    'success': False,
                    'confidence': 0.0,
                    'reason': f"未找到指标 {criteria.metric_name}",
                    'current_value': None,
                    'threshold': criteria.threshold_value
                }

            # 执行比较判断
            success = self._compare_values(
                metric_value, criteria.operator, criteria.threshold_value
            )

            return {
                'criteria': criteria.description,
                'success': success,
                'confidence': criteria.confidence,
                'reason': f"当前值 {metric_value} {criteria.operator} {criteria.threshold_value}",
                'current_value': metric_value,
                'threshold': criteria.threshold_value,
                'operator': criteria.operator,
                'unit': criteria.unit
            }

        except Exception as e:
            return {
                'criteria': criteria.description,
                'success': False,
                'confidence': 0.0,
                'reason': f"检测异常: {str(e)}",
                'current_value': None,
                'threshold': criteria.threshold_value
            }

    def _compare_values(self, current: float, operator: str, threshold: float) -> bool:
        """比较数值"""
        if operator == ">=":
            return current >= threshold
        elif operator == "<=":
            return current <= threshold
        elif operator == ">":
            return current > threshold
        elif operator == "<":
            return current < threshold
        elif operator == "=":
            return abs(current - threshold) < 0.001  # 浮点数相等比较
        else:
            return False

    def _generate_baseline_comparison(
        self,
        current_metrics: Dict[str, Any],
        baseline_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """生成基线对比"""
        comparison = {}

        for metric_name, current_value in current_metrics.items():
            if isinstance(current_value, (int, float)):
                baseline_value = baseline_data.get(metric_name, 0.0)
                if baseline_value > 0:
                    change_percent = ((current_value - baseline_value) / baseline_value) * 100
                    comparison[metric_name] = {
                        'current': current_value,
                        'baseline': baseline_value,
                        'change_percent': change_percent,
                        'improved': change_percent > 0
                    }

        return comparison

    def _generate_recommendations(
        self,
        criteria_results: List[Dict[str, Any]],
        baseline_comparison: Dict[str, Any]
    ) -> List[str]:
        """生成改进建议"""
        recommendations = []

        # 基于失败的标准生成建议
        for result in criteria_results:
            if not result['success']:
                recommendations.append(
                    f"建议关注 {result['criteria']}，当前值未达到预期标准"
                )

        # 基于基线对比生成建议
        for metric_name, comparison in baseline_comparison.items():
            if comparison['change_percent'] < 0:
                recommendations.append(
                    f"指标 {metric_name} 相比基线下降了 {abs(comparison['change_percent']):.1f}%，建议进一步优化"
                )

        return recommendations

    async def _update_detection_status(
        self,
        action: OptimizationAction,
        result: DetectionResult
    ):
        """更新检测状态"""
        try:
            # 这里应该更新OptimizationAction的检测字段
            # 由于我们没有直接的数据库更新机制，这里记录日志
            self.logger.info(
                f"更新检测状态: {action.action_id} -> {result.detection_status.value}"
            )
        except Exception as e:
            self.logger.error(f"更新检测状态失败: {e}")

    async def _log_detection_result(self, result: DetectionResult):
        """记录检测结果到数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                INSERT INTO optimization_detection_log (
                    action_id, detection_type, criteria_parsed, metrics_collected,
                    comparison_result, detection_status, confidence_score,
                    execution_time, error_message
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                result.action_id,
                "automatic",
                json.dumps(result.criteria_results),
                json.dumps(result.metrics_collected),
                json.dumps(result.baseline_comparison),
                result.detection_status.value,
                result.confidence,
                result.execution_duration,
                result.error_message
            ))

            conn.commit()
            conn.close()

            self.logger.info(f"检测结果已记录: {result.action_id}")

        except Exception as e:
            self.logger.error(f"记录检测结果失败: {e}")

    def _create_failed_result(
        self,
        action_id: str,
        error_message: str,
        start_time: datetime
    ) -> DetectionResult:
        """创建失败的检测结果"""
        return DetectionResult(
            action_id=action_id,
            detection_status=DetectionStatus.FAILED,
            success=False,
            confidence=0.0,
            metrics_collected={},
            criteria_results=[],
            baseline_comparison={},
            detection_time=datetime.now(),
            execution_duration=(datetime.now() - start_time).total_seconds(),
            error_message=error_message
        )
