#!/usr/bin/env python3
"""
多维度指标收集器

该模块负责从各个监控系统收集多维度指标，为智能检测提供数据支持。
集成现有的性能监控器、系统监控器等，统一收集和管理指标数据。

作者: Augment Code AI Assistant
创建日期: 2025-08-13
版本: 1.0.0
"""

import logging
import sqlite3
import asyncio
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
from collections import defaultdict, deque

# 导入现有监控系统
try:
    from src.monitoring.performance_monitor import PerformanceMonitor
    from src.fusion.performance_monitor import PerformanceMonitor as FusionPerformanceMonitor
    from src.optimization.enhanced_performance_monitor import EnhancedPerformanceMonitor
except ImportError:
    # 如果导入失败，使用模拟类
    PerformanceMonitor = None
    FusionPerformanceMonitor = None
    EnhancedPerformanceMonitor = None

logger = logging.getLogger(__name__)

class MetricCategory(Enum):
    """指标类别枚举"""
    ACCURACY = "accuracy"           # 准确率指标
    PERFORMANCE = "performance"     # 性能指标
    STABILITY = "stability"         # 稳定性指标
    SYSTEM = "system"              # 系统指标
    DATABASE = "database"          # 数据库指标
    PREDICTION = "prediction"      # 预测指标
    USER_EXPERIENCE = "user_experience"  # 用户体验指标

@dataclass
class MetricData:
    """指标数据结构"""
    name: str                      # 指标名称
    value: float                   # 指标值
    category: MetricCategory       # 指标类别
    timestamp: datetime            # 时间戳
    unit: str = ""                 # 单位
    source: str = ""               # 数据源
    metadata: Dict[str, Any] = None  # 元数据

    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}

class MetricsCollector:
    """多维度指标收集器"""
    
    def __init__(self, db_path: str = "data/fucai3d.db"):
        """
        初始化指标收集器
        
        Args:
            db_path: 数据库路径
        """
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
        
        # 指标缓存
        self.metrics_cache: deque = deque(maxlen=10000)
        self.metrics_history: Dict[str, List[MetricData]] = defaultdict(list)
        
        # 监控器实例
        self.performance_monitor = None
        self.fusion_monitor = None
        self.enhanced_monitor = None
        
        # 收集配置
        self.collection_config = {
            "accuracy_window_hours": 24,      # 准确率统计时间窗口
            "performance_window_minutes": 60,  # 性能统计时间窗口
            "stability_window_hours": 12,     # 稳定性统计时间窗口
            "collection_interval": 30,        # 收集间隔(秒)
            "max_history_days": 7            # 历史数据保留天数
        }
        
        # 初始化监控器
        self._init_monitors()
        
        # 初始化数据库
        self._init_database()
        
        self.logger.info("多维度指标收集器初始化完成")
    
    def _init_monitors(self):
        """初始化监控器实例"""
        try:
            # 初始化性能监控器
            if PerformanceMonitor:
                self.performance_monitor = PerformanceMonitor(self.db_path)
                self.logger.info("性能监控器初始化成功")
            
            # 初始化融合监控器
            if FusionPerformanceMonitor:
                config = {"check_interval": 30, "alert_threshold": 0.8}
                self.fusion_monitor = FusionPerformanceMonitor(self.db_path, config)
                self.logger.info("融合监控器初始化成功")
            
            # 初始化增强监控器
            if EnhancedPerformanceMonitor:
                monitor_config = {"monitoring_level": "standard", "collection_interval": 60}
                self.enhanced_monitor = EnhancedPerformanceMonitor(self.db_path, monitor_config)
                self.logger.info("增强监控器初始化成功")
                
        except Exception as e:
            self.logger.warning(f"监控器初始化部分失败: {e}")
    
    def _init_database(self):
        """初始化数据库表"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # 创建指标数据表
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS metrics_data (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        name TEXT NOT NULL,
                        value REAL NOT NULL,
                        category TEXT NOT NULL,
                        unit TEXT,
                        source TEXT,
                        metadata TEXT,
                        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # 创建指标统计表
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS metrics_statistics (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        metric_name TEXT NOT NULL,
                        category TEXT NOT NULL,
                        avg_value REAL,
                        min_value REAL,
                        max_value REAL,
                        std_dev REAL,
                        sample_count INTEGER,
                        time_window_hours INTEGER,
                        calculated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # 创建索引
                conn.execute("CREATE INDEX IF NOT EXISTS idx_metrics_name ON metrics_data(name)")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_metrics_category ON metrics_data(category)")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_metrics_timestamp ON metrics_data(timestamp)")
                
                self.logger.info("指标收集器数据库表初始化完成")
                
        except Exception as e:
            self.logger.error(f"数据库初始化失败: {e}")
    
    async def collect_all_metrics(self) -> List[MetricData]:
        """
        收集所有维度的指标
        
        Returns:
            收集到的指标数据列表
        """
        all_metrics = []
        
        try:
            # 收集准确率指标
            accuracy_metrics = await self._collect_accuracy_metrics()
            all_metrics.extend(accuracy_metrics)
            
            # 收集性能指标
            performance_metrics = await self._collect_performance_metrics()
            all_metrics.extend(performance_metrics)
            
            # 收集稳定性指标
            stability_metrics = await self._collect_stability_metrics()
            all_metrics.extend(stability_metrics)
            
            # 收集系统指标
            system_metrics = await self._collect_system_metrics()
            all_metrics.extend(system_metrics)
            
            # 收集数据库指标
            database_metrics = await self._collect_database_metrics()
            all_metrics.extend(database_metrics)
            
            # 收集用户体验指标
            ux_metrics = await self._collect_user_experience_metrics()
            all_metrics.extend(ux_metrics)
            
            # 缓存指标
            for metric in all_metrics:
                self.metrics_cache.append(metric)
                self.metrics_history[metric.name].append(metric)
            
            # 保存到数据库
            await self._save_metrics_to_db(all_metrics)
            
            self.logger.info(f"成功收集 {len(all_metrics)} 个指标")
            
        except Exception as e:
            self.logger.error(f"收集指标失败: {e}")
        
        return all_metrics
    
    async def _collect_accuracy_metrics(self) -> List[MetricData]:
        """收集准确率指标"""
        metrics = []
        
        try:
            # 从数据库查询最近的预测准确率
            window_start = datetime.now() - timedelta(hours=self.collection_config["accuracy_window_hours"])
            
            with sqlite3.connect(self.db_path) as conn:
                # 百位准确率
                cursor = conn.execute("""
                    SELECT 
                        COUNT(*) as total,
                        SUM(CASE WHEN hundreds_actual = hundreds_predicted THEN 1 ELSE 0 END) as correct
                    FROM prediction_results 
                    WHERE created_at >= ?
                """, (window_start,))
                
                result = cursor.fetchone()
                if result and result[0] > 0:
                    accuracy = (result[1] / result[0]) * 100
                    metrics.append(MetricData(
                        name="hundreds_accuracy",
                        value=accuracy,
                        category=MetricCategory.ACCURACY,
                        timestamp=datetime.now(),
                        unit="%",
                        source="prediction_results",
                        metadata={"total_predictions": result[0], "correct_predictions": result[1]}
                    ))
                
                # 十位准确率
                cursor = conn.execute("""
                    SELECT 
                        COUNT(*) as total,
                        SUM(CASE WHEN tens_actual = tens_predicted THEN 1 ELSE 0 END) as correct
                    FROM prediction_results 
                    WHERE created_at >= ?
                """, (window_start,))
                
                result = cursor.fetchone()
                if result and result[0] > 0:
                    accuracy = (result[1] / result[0]) * 100
                    metrics.append(MetricData(
                        name="tens_accuracy",
                        value=accuracy,
                        category=MetricCategory.ACCURACY,
                        timestamp=datetime.now(),
                        unit="%",
                        source="prediction_results",
                        metadata={"total_predictions": result[0], "correct_predictions": result[1]}
                    ))
                
                # 个位准确率
                cursor = conn.execute("""
                    SELECT 
                        COUNT(*) as total,
                        SUM(CASE WHEN units_actual = units_predicted THEN 1 ELSE 0 END) as correct
                    FROM prediction_results 
                    WHERE created_at >= ?
                """, (window_start,))
                
                result = cursor.fetchone()
                if result and result[0] > 0:
                    accuracy = (result[1] / result[0]) * 100
                    metrics.append(MetricData(
                        name="units_accuracy",
                        value=accuracy,
                        category=MetricCategory.ACCURACY,
                        timestamp=datetime.now(),
                        unit="%",
                        source="prediction_results",
                        metadata={"total_predictions": result[0], "correct_predictions": result[1]}
                    ))
        
        except Exception as e:
            self.logger.warning(f"收集准确率指标失败: {e}")

        return metrics

    async def _collect_performance_metrics(self) -> List[MetricData]:
        """收集性能指标"""
        metrics = []

        try:
            # 从性能监控器获取指标
            if self.performance_monitor:
                # API响应时间
                api_metrics = getattr(self.performance_monitor, 'api_metrics', [])
                if api_metrics:
                    recent_metrics = list(api_metrics)[-100:]  # 最近100个请求
                    if recent_metrics:
                        avg_response_time = sum(m.response_time for m in recent_metrics) / len(recent_metrics)
                        metrics.append(MetricData(
                            name="avg_api_response_time",
                            value=avg_response_time,
                            category=MetricCategory.PERFORMANCE,
                            timestamp=datetime.now(),
                            unit="seconds",
                            source="performance_monitor",
                            metadata={"sample_count": len(recent_metrics)}
                        ))

            # 从融合监控器获取指标
            if self.fusion_monitor:
                current_metrics = self.fusion_monitor.get_current_metrics()
                for name, data in current_metrics.items():
                    if 'execution_time' in name or 'response_time' in name:
                        metrics.append(MetricData(
                            name=name,
                            value=data['value'],
                            category=MetricCategory.PERFORMANCE,
                            timestamp=datetime.now(),
                            unit="seconds",
                            source="fusion_monitor"
                        ))

            # 预测执行时间
            window_start = datetime.now() - timedelta(minutes=self.collection_config["performance_window_minutes"])

            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    SELECT AVG(execution_time), COUNT(*)
                    FROM prediction_performance
                    WHERE created_at >= ?
                """, (window_start,))

                result = cursor.fetchone()
                if result and result[1] > 0:
                    metrics.append(MetricData(
                        name="avg_prediction_time",
                        value=result[0],
                        category=MetricCategory.PERFORMANCE,
                        timestamp=datetime.now(),
                        unit="seconds",
                        source="prediction_performance",
                        metadata={"sample_count": result[1]}
                    ))

        except Exception as e:
            self.logger.warning(f"收集性能指标失败: {e}")

        return metrics

    async def _collect_stability_metrics(self) -> List[MetricData]:
        """收集稳定性指标"""
        metrics = []

        try:
            window_start = datetime.now() - timedelta(hours=self.collection_config["stability_window_hours"])

            with sqlite3.connect(self.db_path) as conn:
                # 系统错误率
                cursor = conn.execute("""
                    SELECT
                        COUNT(*) as total,
                        SUM(CASE WHEN status_code >= 400 THEN 1 ELSE 0 END) as errors
                    FROM api_metrics
                    WHERE timestamp >= ?
                """, (window_start,))

                result = cursor.fetchone()
                if result and result[0] > 0:
                    error_rate = (result[1] / result[0]) * 100
                    metrics.append(MetricData(
                        name="system_error_rate",
                        value=error_rate,
                        category=MetricCategory.STABILITY,
                        timestamp=datetime.now(),
                        unit="%",
                        source="api_metrics",
                        metadata={"total_requests": result[0], "error_count": result[1]}
                    ))

                # 预测成功率
                cursor = conn.execute("""
                    SELECT
                        COUNT(*) as total,
                        SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) as success
                    FROM prediction_performance
                    WHERE created_at >= ?
                """, (window_start,))

                result = cursor.fetchone()
                if result and result[0] > 0:
                    success_rate = (result[1] / result[0]) * 100
                    metrics.append(MetricData(
                        name="prediction_success_rate",
                        value=success_rate,
                        category=MetricCategory.STABILITY,
                        timestamp=datetime.now(),
                        unit="%",
                        source="prediction_performance",
                        metadata={"total_predictions": result[0], "successful_predictions": result[1]}
                    ))

                # 系统可用性
                cursor = conn.execute("""
                    SELECT
                        COUNT(*) as total,
                        SUM(CASE WHEN status_code < 500 THEN 1 ELSE 0 END) as available
                    FROM api_metrics
                    WHERE timestamp >= ?
                """, (window_start,))

                result = cursor.fetchone()
                if result and result[0] > 0:
                    availability = (result[1] / result[0]) * 100
                    metrics.append(MetricData(
                        name="system_availability",
                        value=availability,
                        category=MetricCategory.STABILITY,
                        timestamp=datetime.now(),
                        unit="%",
                        source="api_metrics",
                        metadata={"total_requests": result[0], "available_requests": result[1]}
                    ))

        except Exception as e:
            self.logger.warning(f"收集稳定性指标失败: {e}")

        return metrics

    async def _collect_system_metrics(self) -> List[MetricData]:
        """收集系统指标"""
        metrics = []

        try:
            # 从系统监控器获取指标
            if self.performance_monitor:
                system_metrics = getattr(self.performance_monitor, 'system_metrics', [])
                if system_metrics:
                    latest_metric = list(system_metrics)[-1]

                    metrics.extend([
                        MetricData(
                            name="cpu_usage",
                            value=latest_metric.cpu_usage,
                            category=MetricCategory.SYSTEM,
                            timestamp=datetime.now(),
                            unit="%",
                            source="performance_monitor"
                        ),
                        MetricData(
                            name="memory_usage",
                            value=latest_metric.memory_usage,
                            category=MetricCategory.SYSTEM,
                            timestamp=datetime.now(),
                            unit="%",
                            source="performance_monitor"
                        ),
                        MetricData(
                            name="disk_usage",
                            value=latest_metric.disk_usage,
                            category=MetricCategory.SYSTEM,
                            timestamp=datetime.now(),
                            unit="%",
                            source="performance_monitor"
                        )
                    ])

            # 使用psutil直接获取系统指标
            try:
                import psutil

                metrics.extend([
                    MetricData(
                        name="cpu_percent",
                        value=psutil.cpu_percent(interval=1),
                        category=MetricCategory.SYSTEM,
                        timestamp=datetime.now(),
                        unit="%",
                        source="psutil"
                    ),
                    MetricData(
                        name="memory_percent",
                        value=psutil.virtual_memory().percent,
                        category=MetricCategory.SYSTEM,
                        timestamp=datetime.now(),
                        unit="%",
                        source="psutil"
                    ),
                    MetricData(
                        name="disk_percent",
                        value=psutil.disk_usage('/').percent,
                        category=MetricCategory.SYSTEM,
                        timestamp=datetime.now(),
                        unit="%",
                        source="psutil"
                    )
                ])

            except ImportError:
                self.logger.warning("psutil未安装，跳过直接系统指标收集")

        except Exception as e:
            self.logger.warning(f"收集系统指标失败: {e}")

        return metrics

    async def _collect_database_metrics(self) -> List[MetricData]:
        """收集数据库指标"""
        metrics = []

        try:
            # 数据库连接时间
            start_time = time.time()
            with sqlite3.connect(self.db_path, timeout=5) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT 1")
                cursor.fetchone()
            connection_time = time.time() - start_time

            metrics.append(MetricData(
                name="db_connection_time",
                value=connection_time,
                category=MetricCategory.DATABASE,
                timestamp=datetime.now(),
                unit="seconds",
                source="direct_test"
            ))

            # 数据库大小
            import os
            if os.path.exists(self.db_path):
                db_size_mb = os.path.getsize(self.db_path) / (1024 * 1024)
                metrics.append(MetricData(
                    name="db_size",
                    value=db_size_mb,
                    category=MetricCategory.DATABASE,
                    timestamp=datetime.now(),
                    unit="MB",
                    source="file_system"
                ))

            # 数据库记录统计
            with sqlite3.connect(self.db_path) as conn:
                # 预测记录数
                cursor = conn.execute("SELECT COUNT(*) FROM prediction_results")
                prediction_count = cursor.fetchone()[0]

                metrics.append(MetricData(
                    name="prediction_records_count",
                    value=prediction_count,
                    category=MetricCategory.DATABASE,
                    timestamp=datetime.now(),
                    unit="records",
                    source="database_query"
                ))

                # 最近24小时新增记录
                window_start = datetime.now() - timedelta(hours=24)
                cursor = conn.execute("""
                    SELECT COUNT(*) FROM prediction_results
                    WHERE created_at >= ?
                """, (window_start,))
                recent_count = cursor.fetchone()[0]

                metrics.append(MetricData(
                    name="recent_predictions_24h",
                    value=recent_count,
                    category=MetricCategory.DATABASE,
                    timestamp=datetime.now(),
                    unit="records",
                    source="database_query"
                ))

        except Exception as e:
            self.logger.warning(f"收集数据库指标失败: {e}")

        return metrics

    async def _collect_user_experience_metrics(self) -> List[MetricData]:
        """收集用户体验指标"""
        metrics = []

        try:
            window_start = datetime.now() - timedelta(hours=24)

            with sqlite3.connect(self.db_path) as conn:
                # 用户反馈评分
                cursor = conn.execute("""
                    SELECT AVG(rating), COUNT(*)
                    FROM user_feedback
                    WHERE created_at >= ? AND rating IS NOT NULL
                """, (window_start,))

                result = cursor.fetchone()
                if result and result[1] > 0:
                    metrics.append(MetricData(
                        name="user_satisfaction_score",
                        value=result[0],
                        category=MetricCategory.USER_EXPERIENCE,
                        timestamp=datetime.now(),
                        unit="score",
                        source="user_feedback",
                        metadata={"feedback_count": result[1]}
                    ))

                # 页面加载时间（从API响应时间推算）
                cursor = conn.execute("""
                    SELECT AVG(response_time)
                    FROM api_metrics
                    WHERE timestamp >= ? AND endpoint LIKE '%dashboard%'
                """, (window_start,))

                result = cursor.fetchone()
                if result and result[0]:
                    metrics.append(MetricData(
                        name="page_load_time",
                        value=result[0],
                        category=MetricCategory.USER_EXPERIENCE,
                        timestamp=datetime.now(),
                        unit="seconds",
                        source="api_metrics"
                    ))

        except Exception as e:
            self.logger.warning(f"收集用户体验指标失败: {e}")

        return metrics

    async def _save_metrics_to_db(self, metrics: List[MetricData]):
        """保存指标到数据库"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                for metric in metrics:
                    conn.execute("""
                        INSERT INTO metrics_data
                        (name, value, category, unit, source, metadata, timestamp)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    """, (
                        metric.name,
                        metric.value,
                        metric.category.value,
                        metric.unit,
                        metric.source,
                        str(metric.metadata) if metric.metadata else None,
                        metric.timestamp
                    ))

                self.logger.debug(f"保存 {len(metrics)} 个指标到数据库")

        except Exception as e:
            self.logger.error(f"保存指标到数据库失败: {e}")

    def get_metrics_by_category(self, category: MetricCategory,
                               hours: int = 24) -> List[MetricData]:
        """
        根据类别获取指标

        Args:
            category: 指标类别
            hours: 时间范围(小时)

        Returns:
            指标数据列表
        """
        try:
            window_start = datetime.now() - timedelta(hours=hours)

            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    SELECT name, value, category, unit, source, metadata, timestamp
                    FROM metrics_data
                    WHERE category = ? AND timestamp >= ?
                    ORDER BY timestamp DESC
                """, (category.value, window_start))

                metrics = []
                for row in cursor.fetchall():
                    metrics.append(MetricData(
                        name=row[0],
                        value=row[1],
                        category=MetricCategory(row[2]),
                        timestamp=datetime.fromisoformat(row[6]),
                        unit=row[3] or "",
                        source=row[4] or "",
                        metadata=eval(row[5]) if row[5] else {}
                    ))

                return metrics

        except Exception as e:
            self.logger.error(f"获取指标失败: {e}")
            return []

    def get_latest_metrics(self, limit: int = 100) -> List[MetricData]:
        """
        获取最新的指标

        Args:
            limit: 返回数量限制

        Returns:
            最新的指标数据列表
        """
        return list(self.metrics_cache)[-limit:] if self.metrics_cache else []

    def calculate_metric_statistics(self, metric_name: str,
                                   hours: int = 24) -> Dict[str, float]:
        """
        计算指标统计信息

        Args:
            metric_name: 指标名称
            hours: 时间范围(小时)

        Returns:
            统计信息字典
        """
        try:
            window_start = datetime.now() - timedelta(hours=hours)

            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    SELECT value FROM metrics_data
                    WHERE name = ? AND timestamp >= ?
                """, (metric_name, window_start))

                values = [row[0] for row in cursor.fetchall()]

                if not values:
                    return {}

                import statistics

                stats = {
                    "count": len(values),
                    "avg": statistics.mean(values),
                    "min": min(values),
                    "max": max(values),
                    "median": statistics.median(values)
                }

                if len(values) > 1:
                    stats["std_dev"] = statistics.stdev(values)
                else:
                    stats["std_dev"] = 0.0

                return stats

        except Exception as e:
            self.logger.error(f"计算指标统计失败: {e}")
            return {}

    async def start_continuous_collection(self):
        """启动持续收集"""
        self.logger.info("启动持续指标收集")

        while True:
            try:
                await self.collect_all_metrics()
                await asyncio.sleep(self.collection_config["collection_interval"])

            except Exception as e:
                self.logger.error(f"持续收集过程中发生错误: {e}")
                await asyncio.sleep(60)  # 错误时等待1分钟再重试
